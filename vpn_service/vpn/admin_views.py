"""
Административные API views для VPN приложения.
"""
import logging
import json
from django.utils import timezone
from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from drf_spectacular.utils import extend_schema, OpenApiExample

from .permissions import IsAdminUser
from .admin_serializers import (
    AdminGenerateConfigRequestSerializer,
    AdminGenerateConfigResponseSerializer,
    AdminGenerateConfigErrorSerializer,
    UserInfoSerializer,
    LocationInfoSerializer
)
from .trojan_service import PersonalizedSingBoxService
from .models import Location
from accounts.models import UserAccount, HiddifyLink
from subscriptions.models import ActiveSubscription

logger = logging.getLogger(__name__)


@extend_schema(
    tags=['Admin'],
    summary='Generate personalized SingBox config',
    description="""
    Административный endpoint для генерации персонализированных SingBox конфигураций.
    
    **Функциональность:**
    - Генерирует уникальную SingBox конфигурацию для указанного пользователя
    - Использует данные пользователя из Hiddify Manager
    - Применяет параметры локации из активной подписки
    - Возвращает конфигурацию в формате, идентичном singbox_Config_example
    
    **Права доступа:**
    - Только администраторы (staff пользователи)
    - Требуется JWT аутентификация
    
    **Параметры:**
    - `user_id`: UUID пользователя в системе
    - `force_recreate`: Принудительно пересоздать пользователя в Hiddify (опционально)
    
    **Логика работы:**
    1. Проверяет существование пользователя
    2. Определяет активную подписку и доступные локации
    3. Получает или создает пользователя в Hiddify Manager
    4. Генерирует персонализированную SingBox конфигурацию
    5. Возвращает конфигурацию с метаданными
    """,
    request=AdminGenerateConfigRequestSerializer,
    responses={
        200: AdminGenerateConfigResponseSerializer,
        400: AdminGenerateConfigErrorSerializer,
        401: AdminGenerateConfigErrorSerializer,
        403: AdminGenerateConfigErrorSerializer,
        404: AdminGenerateConfigErrorSerializer,
        500: AdminGenerateConfigErrorSerializer
    },
    examples=[
        OpenApiExample(
            'Request Example',
            description='Пример запроса генерации конфигурации',
            value={
                "user_id": "123e4567-e89b-12d3-a456-************",
                "force_recreate": False
            },
            request_only=True
        ),
        OpenApiExample(
            'Success Response',
            description='Успешная генерация конфигурации',
            value={
                "success": True,
                "user_info": {
                    "id": "123e4567-e89b-12d3-a456-************",
                    "email": "<EMAIL>",
                    "is_anonymous": False,
                    "is_active": True,
                    "active_subscription": {
                        "plan_name": "Premium Monthly",
                        "expires_at": "2025-07-14T12:00:00Z",
                        "traffic_limit_gb": 100,
                        "max_devices": 5
                    },
                    "device_count": 2
                },
                "hiddify_user_uuid": "15c175d8-703c-456a-ac82-91041f8af845",
                "location_info": {
                    "id": "loc-uuid-123",
                    "name": "Netherlands - Amsterdam",
                    "country_code": "NL",
                    "city": "Amsterdam",
                    "flag_emoji": "🇳🇱"
                },
                "config": {
                    "dns": {
                        "servers": [
                            {
                                "tag": "cloudflare",
                                "address": "https://*******/dns-query",
                                "address_resolver": "local",
                                "detour": "proxy"
                            }
                        ],
                        "final": "cloudflare",
                        "strategy": "ipv4_only"
                    },
                    "inbounds": [
                        {
                            "type": "tun",
                            "inet4_address": "**********/30",
                            "auto_route": True,
                            "sniff": True
                        }
                    ],
                    "outbounds": [
                        {
                            "type": "selector",
                            "tag": "proxy",
                            "outbounds": ["trojan-ws", "vmess-ws", "trojan-grpc", "vmess-grpc"]
                        },
                        {
                            "type": "trojan",
                            "tag": "trojan-ws",
                            "server": "***********",
                            "server_port": 443,
                            "password": "15c175d8-703c-456a-ac82-91041f8af845",
                            "tls": {
                                "enabled": True,
                                "server_name": "***********.sslip.io"
                            },
                            "transport": {
                                "type": "ws",
                                "path": "/Cgm6B1DqLOKIFrY19tjCyr3egnx"
                            }
                        }
                    ],
                    "route": {
                        "final": "proxy",
                        "auto_detect_interface": True
                    }
                },
                "metadata": {
                    "generated_at": "2025-06-14T19:45:00Z",
                    "config_format": "singbox_json",
                    "protocols": ["trojan", "vmess"],
                    "transports": ["websocket", "grpc"],
                    "admin_generated": True
                }
            },
            response_only=True
        ),
        OpenApiExample(
            'Error Response - User Not Found',
            description='Пользователь не найден',
            value={
                "success": False,
                "error": "User not found",
                "error_code": "USER_NOT_FOUND",
                "details": {
                    "user_id": "123e4567-e89b-12d3-a456-************"
                }
            },
            response_only=True
        ),
        OpenApiExample(
            'Error Response - No Subscription',
            description='У пользователя нет активной подписки',
            value={
                "success": False,
                "error": "User has no active subscription",
                "error_code": "NO_ACTIVE_SUBSCRIPTION",
                "details": {
                    "user_id": "123e4567-e89b-12d3-a456-************"
                }
            },
            response_only=True
        )
    ]
)
@api_view(['POST'])
@permission_classes([IsAdminUser])
def generate_personalized_config(request):
    """
    Генерирует персонализированную SingBox конфигурацию для указанного пользователя.
    
    PURPOSE:
      - Предоставляет администраторам возможность генерировать конфигурации для любого пользователя
      - Обеспечивает полный контроль над процессом генерации конфигураций
      - Позволяет принудительно пересоздавать пользователей в Hiddify Manager
      - Возвращает конфигурацию в формате, идентичном singbox_Config_example
    
    AAG (Actor -> Action -> Goal):
      - Администратор -> Запрашивает конфигурацию для пользователя -> Получает готовую SingBox конфигурацию
      - Система -> Валидирует данные -> Генерирует персонализированную конфигурацию
      - Hiddify Manager -> Предоставляет данные пользователя -> Обеспечивает аутентификацию VPN
    
    CONTRACT:
      PRECONDITIONS:
        - request.user.is_staff == True (проверяется permission_classes)
        - Валидный JSON в теле запроса с user_id
        - Пользователь существует в системе
      POSTCONDITIONS:
        - Возвращается персонализированная SingBox конфигурация
        - Конфигурация соответствует формату singbox_Config_example
        - UUID пользователя подставлен во все необходимые поля
      INVARIANTS:
        - Структура конфигурации всегда соответствует базовому шаблону
        - Серверные параметры берутся из выбранной локации
        - Метаданные содержат информацию о генерации
    
    ARGS:
      - request: HTTP запрос с JSON данными
    
    RETURNS:
      - Response: JSON ответ с конфигурацией или ошибкой
    """
    try:
        # Шаг 1: Валидация входных данных
        serializer = AdminGenerateConfigRequestSerializer(data=request.data)
        if not serializer.is_valid():
            logger.warning(f"Admin config generation: Invalid request data from {request.user.email}: {serializer.errors}")
            return Response({
                'success': False,
                'error': 'Invalid request data',
                'error_code': 'VALIDATION_ERROR',
                'details': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)
        
        validated_data = serializer.validated_data
        user_account = serializer.get_validated_user()
        force_recreate = validated_data.get('force_recreate', False)
        
        logger.info(f"Admin {request.user.email} requesting config generation for user {user_account.email} (force_recreate={force_recreate})")
        
        # Шаг 2: Проверка активной подписки
        active_subscription = ActiveSubscription.objects.filter(
            user=user_account,
            is_active=True,
            start_date__lte=timezone.now(),
            end_date__gte=timezone.now()
        ).select_related('plan').first()
        
        if not active_subscription:
            logger.warning(f"Admin config generation: User {user_account.email} has no active subscription")
            return Response({
                'success': False,
                'error': 'User has no active subscription',
                'error_code': 'NO_ACTIVE_SUBSCRIPTION',
                'details': {
                    'user_id': str(user_account.id),
                    'user_email': user_account.email
                }
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Шаг 3: Определение локации через промежуточную модель
        from .models import SubscriptionPlanLocation

        available_plan_locations = SubscriptionPlanLocation.objects.filter(
            plan=active_subscription.plan,
            location__is_active=True
        ).select_related('location')

        if not available_plan_locations.exists():
            logger.error(f"Admin config generation: No available locations for user {user_account.email}")
            return Response({
                'success': False,
                'error': 'No available locations for user subscription',
                'error_code': 'NO_AVAILABLE_LOCATIONS',
                'details': {
                    'user_id': str(user_account.id),
                    'plan_name': active_subscription.plan.name
                }
            }, status=status.HTTP_404_NOT_FOUND)

        # Используем дефолтную локацию или первую доступную
        default_plan_location = available_plan_locations.filter(is_default=True).first()
        target_plan_location = default_plan_location or available_plan_locations.first()
        target_location = target_plan_location.location
        
        # Шаг 4: Получение или создание Hiddify пользователя
        hiddify_link = None
        hiddify_user_uuid = None
        
        try:
            hiddify_link = HiddifyLink.objects.get(
                user=user_account,
                is_active_in_hiddify=True
            )
            hiddify_user_uuid = str(hiddify_link.hiddify_user_uuid)
            
            if force_recreate:
                logger.info(f"Force recreating Hiddify user for {user_account.email}")
                # Логика принудительного пересоздания может быть добавлена здесь
                
        except HiddifyLink.DoesNotExist:
            logger.info(f"No Hiddify link found for user {user_account.email}, will use existing UUID or create new")
            # Если нет связи с Hiddify, используем ID пользователя как UUID
            hiddify_user_uuid = str(user_account.id)
        
        # Шаг 5: Генерация персонализированной конфигурации
        result = PersonalizedSingBoxService.get_config_for_existing_user(
            hiddify_user_uuid=hiddify_user_uuid,
            location_params=target_location.hiddify_params,
            user_name=user_account.email or f"user_{user_account.id}"
        )
        
        if not result['success']:
            logger.error(f"Failed to generate config for user {user_account.email}: {result.get('error')}")
            return Response({
                'success': False,
                'error': 'Failed to generate configuration',
                'error_code': 'CONFIG_GENERATION_FAILED',
                'details': {
                    'user_id': str(user_account.id),
                    'hiddify_error': result.get('error', 'Unknown error')
                }
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        # Шаг 6: Подготовка ответа
        user_serializer = UserInfoSerializer(user_account)
        location_serializer = LocationInfoSerializer({
            'id': target_location.id,
            'name': target_location.name,
            'country_code': target_location.country_code,
            'city': target_location.city,
            'flag_emoji': target_location.flag_emoji,
            'is_active': target_location.is_active,
            'server_info': {
                'server': target_location.hiddify_params.get('server', 'unknown'),
                'server_port': target_location.hiddify_params.get('server_port', 443)
            }
        })
        
        response_data = {
            'success': True,
            'user_info': user_serializer.data,
            'hiddify_user_uuid': hiddify_user_uuid,
            'location_info': location_serializer.data,
            'config': result['singbox_config'],
            'metadata': {
                'generated_at': timezone.now().isoformat(),
                'generated_by_admin': request.user.email,
                'config_format': 'singbox_json',
                'protocols': ['trojan', 'vmess'],
                'transports': ['websocket', 'grpc', 'httpupgrade'],
                'admin_generated': True,
                'force_recreate': force_recreate
            }
        }
        
        logger.info(f"Successfully generated config for user {user_account.email} by admin {request.user.email}")
        return Response(response_data, status=status.HTTP_200_OK)
        
    except Exception as e:
        logger.error(f"Unexpected error in admin config generation by {request.user.email}: {str(e)}", exc_info=True)
        return Response({
            'success': False,
            'error': 'Internal server error',
            'error_code': 'INTERNAL_ERROR',
            'details': {
                'message': str(e)
            }
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
