"""
Permissions классы для VPN приложения.
"""
from rest_framework import permissions


class IsAdminUser(permissions.BasePermission):
    """
    Разрешение только для администраторов (staff пользователей).
    
    PURPOSE:
      - Ограничивает доступ к административным API endpoints
      - Проверяе<PERSON>, что пользователь является staff member
      - Обеспечивает безопасность административных функций
    
    AAG (Actor -> Action -> Goal):
      - Администратор -> Обращается к admin API -> Получает доступ к функциям
      - Обычный пользователь -> Обращается к admin API -> Получает отказ в доступе
    
    CONTRACT:
      PRECONDITIONS:
        - request: HTTP запрос с аутентифицированным пользователем
      POSTCONDITIONS:
        - True если пользователь является staff, False иначе
      INVARIANTS:
        - Проверка выполняется для каждого запроса
        - Анонимные пользователи всегда получают False
    """
    
    def has_permission(self, request, view):
        """
        Проверяет, является ли пользователь администратором.
        
        ARGS:
          - request: HTTP запрос
          - view: View класс
        
        RETURNS:
          - bool: True если пользователь администратор, False иначе
        """
        return (
            request.user and
            request.user.is_authenticated and
            request.user.is_staff
        )


class IsSuperUser(permissions.BasePermission):
    """
    Разрешение только для суперпользователей.
    
    PURPOSE:
      - Ограничивает доступ к критически важным функциям
      - Проверяет, что пользователь является суперпользователем
      - Обеспечивает дополнительный уровень безопасности
    """
    
    def has_permission(self, request, view):
        """
        Проверяет, является ли пользователь суперпользователем.
        
        ARGS:
          - request: HTTP запрос
          - view: View класс
        
        RETURNS:
          - bool: True если пользователь суперпользователь, False иначе
        """
        return (
            request.user and
            request.user.is_authenticated and
            request.user.is_superuser
        )


class IsAdminOrReadOnly(permissions.BasePermission):
    """
    Разрешение на чтение для всех, на изменение только для администраторов.
    
    PURPOSE:
      - Позволяет всем пользователям читать данные
      - Ограничивает изменения только администраторами
      - Используется для публичных справочников с административным управлением
    """
    
    def has_permission(self, request, view):
        """
        Проверяет права доступа в зависимости от метода запроса.
        
        ARGS:
          - request: HTTP запрос
          - view: View класс
        
        RETURNS:
          - bool: True если доступ разрешен, False иначе
        """
        # Разрешаем чтение всем аутентифицированным пользователям
        if request.method in permissions.SAFE_METHODS:
            return request.user and request.user.is_authenticated
        
        # Разрешаем изменения только администраторам
        return (
            request.user and
            request.user.is_authenticated and
            request.user.is_staff
        )
