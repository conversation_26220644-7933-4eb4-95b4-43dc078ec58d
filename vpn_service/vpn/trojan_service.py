"""
Сервис для генерации Trojan VPN конфигураций.
Интеграция с Hiddify Manager для протокола Trojan.
"""
import logging
import json
from typing import Dict, Optional
from django.conf import settings

logger = logging.getLogger(__name__)


class TrojanConfigService:
    """
    Сервис для генерации Trojan конфигураций в формате SingBox.
    Поддерживает TUN режим для полного VPN функционала.
    """
    
    @staticmethod
    def generate_trojan_tun_config(
        server: str = "ductuspro.ru",
        server_port: int = 443,
        password: str = None,
        ws_path: str = None,
        user_uuid: str = None
    ) -> Dict:
        """
        Генерирует Trojan конфигурацию с TUN интерфейсом.
        
        ARGS:
            - server: Сервер для подключения
            - server_port: Порт сервера
            - password: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Trojan (UUID пользователя)
            - ws_path: WebSocket путь
            - user_uuid: UUID пользователя для логирования
        
        RETURNS:
            - Dict: SingBox конфигурация
        """
        
        # Используем реальные параметры из рабочей конфигурации
        if not password:
            password = "15c175d8-703c-456a-ac82-91041f8af845"
        
        if not ws_path:
            ws_path = "/Cgm6B1DqLOKIFrY19tjCyr3egnx"
        
        config = {
            "log": {
                "level": "info",
                "timestamp": True
            },
            "dns": {
                "servers": [
                    {
                        "tag": "dns_proxy",
                        "address": "https://*******/dns-query",
                        "address_resolver": "dns_resolver",
                        "strategy": "ipv4_only",
                        "detour": "DUCTUSPRO_TROJAN"
                    },
                    {
                        "tag": "dns_resolver",
                        "address": "*******",
                        "detour": "direct"
                    }
                ],
                "rules": [
                    {
                        "outbound": "any",
                        "server": "dns_resolver"
                    }
                ]
            },
            "inbounds": [
                {
                    "type": "tun",
                    "tag": "tun-in",
                    "interface_name": "tun0",
                    "inet4_address": "**********/30",
                    "mtu": 9000,
                    "auto_route": True,
                    "strict_route": True,
                    "endpoint_independent_nat": False,
                    "stack": "system",
                    "sniff": True,
                    "sniff_override_destination": True
                }
            ],
            "outbounds": [
                {
                    "type": "trojan",
                    "tag": "DUCTUSPRO_TROJAN",
                    "server": server,
                    "server_port": server_port,
                    "password": password,
                    "tls": {
                        "enabled": True,
                        "server_name": server
                    },
                    "transport": {
                        "type": "ws",
                        "path": ws_path,
                        "headers": {
                            "Host": server
                        }
                    }
                },
                {
                    "type": "direct",
                    "tag": "direct"
                },
                {
                    "type": "block",
                    "tag": "block"
                },
                {
                    "type": "dns",
                    "tag": "dns-out"
                }
            ],
            "route": {
                "auto_detect_interface": True,
                "final": "DUCTUSPRO_TROJAN",
                "rules": [
                    {
                        "protocol": "dns",
                        "outbound": "dns-out"
                    },
                    {
                        "geoip": "private",
                        "outbound": "direct"
                    }
                ]
            }
        }
        
        logger.info(f"Generated Trojan TUN config for user {user_uuid} on server {server}")
        return config
    
    @staticmethod
    def generate_trojan_mixed_config(
        server: str = "ductuspro.ru",
        server_port: int = 443,
        password: str = None,
        ws_path: str = None,
        listen_port: int = 2080,
        user_uuid: str = None
    ) -> Dict:
        """
        Генерирует Trojan конфигурацию с Mixed интерфейсом (HTTP/SOCKS прокси).
        
        ARGS:
            - server: Сервер для подключения
            - server_port: Порт сервера
            - password: Пароль Trojan
            - ws_path: WebSocket путь
            - listen_port: Локальный порт для прокси
            - user_uuid: UUID пользователя
        
        RETURNS:
            - Dict: SingBox конфигурация
        """
        
        if not password:
            password = "15c175d8-703c-456a-ac82-91041f8af845"
        
        if not ws_path:
            ws_path = "/Cgm6B1DqLOKIFrY19tjCyr3egnx"
        
        config = {
            "log": {
                "level": "info",
                "timestamp": True
            },
            "dns": {
                "servers": [
                    {
                        "tag": "google",
                        "address": "*******"
                    },
                    {
                        "tag": "local",
                        "address": "local",
                        "detour": "direct"
                    }
                ],
                "rules": [
                    {
                        "outbound": "any",
                        "server": "local"
                    }
                ]
            },
            "inbounds": [
                {
                    "type": "mixed",
                    "tag": "mixed-in",
                    "listen": "127.0.0.1",
                    "listen_port": listen_port
                }
            ],
            "outbounds": [
                {
                    "type": "trojan",
                    "tag": "trojan-ws",
                    "server": server,
                    "server_port": server_port,
                    "password": password,
                    "transport": {
                        "type": "ws",
                        "path": ws_path,
                        "headers": {
                            "Host": server
                        }
                    },
                    "tls": {
                        "enabled": True,
                        "server_name": server,
                        "insecure": False,
                        "alpn": ["h2", "http/1.1"]
                    }
                },
                {
                    "type": "direct",
                    "tag": "direct"
                },
                {
                    "type": "block",
                    "tag": "block"
                }
            ],
            "route": {
                "rules": [
                    {
                        "ip_is_private": True,
                        "outbound": "direct"
                    }
                ],
                "final": "trojan-ws",
                "auto_detect_interface": True
            }
        }
        
        logger.info(f"Generated Trojan Mixed config for user {user_uuid} on port {listen_port}")
        return config
    
    @staticmethod
    def get_trojan_connection_info() -> Dict:
        """
        Возвращает информацию о Trojan подключении для нашего сервера.
        
        RETURNS:
            - Dict: Информация о подключении
        """
        return {
            "protocol": "trojan",
            "server": "ductuspro.ru",
            "server_port": 443,
            "transport": "websocket",
            "ws_path": "/Cgm6B1DqLOKIFrY19tjCyr3egnx",
            "tls_enabled": True,
            "server_name": "ductuspro.ru",
            "alpn": ["h2", "http/1.1"]
        }
