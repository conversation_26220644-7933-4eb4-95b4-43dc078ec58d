"""
Serializers для административных API endpoints VPN приложения.
"""
from rest_framework import serializers
from accounts.models import UserAccount


class AdminGenerateConfigRequestSerializer(serializers.Serializer):
    """
    Serializer для запроса генерации персонализированной конфигурации администратором.
    
    PURPOSE:
      - Валидирует входные данные для административного endpoint
      - Обеспечивает корректность параметров запроса
      - Предоставляет документацию для Swagger UI
    
    AAG (Actor -> Action -> Goal):
      - Администратор -> Отправляет запрос -> Получает валидированные данные
      - Система -> Валидирует данные -> Генерирует конфигурацию
    
    CONTRACT:
      PRECONDITIONS:
        - user_id: UUID существующего пользователя
        - force_recreate: boolean (опционально)
      POSTCONDITIONS:
        - Возвращает валидированные данные
        - Проверяет существование пользователя
      INVARIANTS:
        - user_id всегда валидный UUID
        - force_recreate всегда boolean
    """
    
    user_id = serializers.UUIDField(
        help_text="UUID пользователя для генерации конфигурации",
        required=True
    )
    
    force_recreate = serializers.BooleanField(
        help_text="Принудительно пересоздать пользователя в Hiddify Manager (если уже существует)",
        required=False,
        default=False
    )
    
    def validate_user_id(self, value):
        """
        Проверяет, что пользователь существует.
        
        PURPOSE:
          - Убеждается, что указанный пользователь существует в системе
          - Предотвращает генерацию конфигураций для несуществующих пользователей
        
        ARGS:
          - value (UUID): UUID пользователя
        
        RETURNS:
          - UUID: Валидированный UUID пользователя
        
        RAISES:
          - ValidationError: Если пользователь не найден
        """
        try:
            user = UserAccount.objects.get(id=value)
            # Сохраняем объект пользователя для использования в view
            self._validated_user = user
            return value
        except UserAccount.DoesNotExist:
            raise serializers.ValidationError(f"User with ID {value} does not exist")
    
    def get_validated_user(self):
        """
        Возвращает валидированный объект пользователя.
        
        PURPOSE:
          - Предоставляет доступ к объекту пользователя после валидации
          - Избегает повторных запросов к базе данных
        
        RETURNS:
          - UserAccount: Объект пользователя
        """
        return getattr(self, '_validated_user', None)


class AdminGenerateConfigResponseSerializer(serializers.Serializer):
    """
    Serializer для ответа административного endpoint генерации конфигурации.
    
    PURPOSE:
      - Документирует структуру ответа для Swagger UI
      - Обеспечивает консистентность формата ответа
      - Предоставляет примеры для разработчиков
    """
    
    success = serializers.BooleanField(
        help_text="Статус успешности операции"
    )
    
    user_info = serializers.DictField(
        help_text="Информация о пользователе",
        child=serializers.CharField(),
        required=False
    )
    
    hiddify_user_uuid = serializers.UUIDField(
        help_text="UUID пользователя в Hiddify Manager",
        required=False
    )
    
    location_info = serializers.DictField(
        help_text="Информация о выбранной локации",
        child=serializers.CharField(),
        required=False
    )
    
    config = serializers.DictField(
        help_text="Персонализированная SingBox конфигурация в JSON формате"
    )
    
    metadata = serializers.DictField(
        help_text="Метаданные о сгенерированной конфигурации",
        child=serializers.CharField(),
        required=False
    )


class AdminGenerateConfigErrorSerializer(serializers.Serializer):
    """
    Serializer для ошибок административного endpoint.
    
    PURPOSE:
      - Документирует возможные ошибки для Swagger UI
      - Обеспечивает консистентность формата ошибок
      - Помогает в отладке и мониторинге
    """
    
    success = serializers.BooleanField(
        default=False,
        help_text="Статус операции (всегда false для ошибок)"
    )
    
    error = serializers.CharField(
        help_text="Описание ошибки"
    )
    
    error_code = serializers.CharField(
        help_text="Код ошибки для программной обработки",
        required=False
    )
    
    details = serializers.DictField(
        help_text="Дополнительные детали ошибки",
        child=serializers.CharField(),
        required=False
    )


class UserInfoSerializer(serializers.ModelSerializer):
    """
    Serializer для информации о пользователе в административных ответах.
    
    PURPOSE:
      - Предоставляет безопасную информацию о пользователе
      - Исключает чувствительные данные (пароли, токены)
      - Используется в составе других serializers
    """
    
    active_subscription = serializers.SerializerMethodField()
    device_count = serializers.SerializerMethodField()
    
    class Meta:
        model = UserAccount
        fields = [
            'id', 'email', 'is_anonymous', 'is_active', 
            'date_joined', 'last_login', 'active_subscription', 'device_count'
        ]
        read_only_fields = fields
    
    def get_active_subscription(self, obj):
        """
        Возвращает информацию об активной подписке пользователя.
        
        ARGS:
          - obj (UserAccount): Объект пользователя
        
        RETURNS:
          - dict: Информация о подписке или None
        """
        from subscriptions.models import ActiveSubscription
        from django.utils import timezone
        
        active_subscription = ActiveSubscription.objects.filter(
            user=obj,
            is_active=True,
            start_date__lte=timezone.now(),
            end_date__gte=timezone.now()
        ).select_related('plan').first()
        
        if active_subscription:
            return {
                'plan_name': active_subscription.plan.name,
                'expires_at': active_subscription.end_date.isoformat(),
                'traffic_limit_gb': active_subscription.plan.traffic_limit_gb,
                'max_devices': active_subscription.plan.max_devices
            }
        return None
    
    def get_device_count(self, obj):
        """
        Возвращает количество устройств пользователя.
        
        ARGS:
          - obj (UserAccount): Объект пользователя
        
        RETURNS:
          - int: Количество активных устройств
        """
        return obj.devices.filter(is_active=True).count()


class LocationInfoSerializer(serializers.Serializer):
    """
    Serializer для информации о локации в административных ответах.
    
    PURPOSE:
      - Предоставляет информацию о выбранной локации
      - Документирует структуру данных локации
      - Используется в составе ответов конфигурации
    """
    
    id = serializers.UUIDField(help_text="UUID локации")
    name = serializers.CharField(help_text="Название локации")
    country_code = serializers.CharField(help_text="Код страны")
    city = serializers.CharField(help_text="Город", required=False)
    flag_emoji = serializers.CharField(help_text="Эмодзи флага", required=False)
    is_active = serializers.BooleanField(help_text="Активна ли локация")
    server_info = serializers.DictField(
        help_text="Информация о сервере локации",
        child=serializers.CharField(),
        required=False
    )
