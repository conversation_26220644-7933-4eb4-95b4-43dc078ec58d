"""
URL configuration for VPN app.
"""
from django.urls import path, include
from . import views, connection_views

urlpatterns = [
    # Классические VPN конфигурации (Stage 1)
    path('config/', views.get_vpn_config, name='vpn_config'),

    # Trojan VPN конфигурации
    path('trojan/', views.get_trojan_config, name='trojan_config'),

    # VPN Locations
    path('locations/', views.get_available_locations, name='vpn_locations'),

    # Traffic Statistics
    path('stats/', views.get_traffic_stats, name='traffic_stats'),

    # Connection Status (Stage 2 requirement)
    path('connection/status/', connection_views.connection_status, name='connection_status'),
    path('connection/logs/', connection_views.get_connection_logs, name='connection_logs'),

    # Современные VPN конфигурации (Stage 2+)
    path('', include('vpn.modern_urls')),
]
