#!/usr/bin/env python3
"""
Генерация конфигурации для нового пользователя через admin endpoint
"""
import requests
import json
import time
from datetime import datetime

def generate_config_for_new_user(user_email):
    """Генерирует конфигурацию для нового пользователя"""
    
    url = "http://localhost:8090/api/vpn/admin/simple-generate-config/"
    headers = {
        "Content-Type": "application/json",
        "X-API-Key": "be84eb6e-cf9d-4b2b-b063-fdf26960ebca"
    }
    data = {
        "user_email": user_email
    }
    
    print(f"🚀 Генерация конфигурации для нового пользователя: {user_email}")
    print(f"📡 URL: {url}")
    print("-" * 60)
    
    try:
        start_time = time.time()
        response = requests.post(url, headers=headers, json=data, timeout=30)
        end_time = time.time()
        
        print(f"📊 HTTP Status: {response.status_code}")
        print(f"⏱️  Response time: {end_time - start_time:.2f}s")
        print(f"📦 Response size: {len(response.text)} bytes")
        print("-" * 60)
        
        if response.status_code == 200:
            config_data = response.json()
            
            # Сохраняем конфигурацию в файл
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"new_user_config_{user_email.replace('@', '_').replace('.', '_')}_{timestamp}.json"
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            print(f"✅ Конфигурация успешно сгенерирована!")
            print(f"💾 Сохранено в файл: {filename}")
            
            # Анализируем конфигурацию
            print("\n=== АНАЛИЗ КОНФИГУРАЦИИ ===")
            print(f"🔧 Основные разделы: {list(config_data.keys())}")
            
            if 'outbounds' in config_data:
                outbounds = config_data['outbounds']
                print(f"🌐 Количество outbounds: {len(outbounds)}")
                
                # Находим протоколы
                protocols = set()
                user_uuid = None
                server = None
                
                for outbound in outbounds:
                    if 'type' in outbound and outbound['type'] in ['trojan', 'vmess']:
                        protocols.add(outbound['type'])
                        if 'server' in outbound:
                            server = outbound['server']
                        if 'password' in outbound:
                            user_uuid = outbound['password']
                        elif 'uuid' in outbound:
                            user_uuid = outbound['uuid']
                
                print(f"🔐 Протоколы: {', '.join(protocols)}")
                print(f"🌍 Сервер: {server}")
                print(f"🆔 UUID пользователя: {user_uuid}")
            
            # Проверяем количество строк
            lines_count = len(response.text.split('\n'))
            print(f"📄 Количество строк: {lines_count}")
            
            return True, filename, config_data
            
        else:
            print(f"❌ Ошибка HTTP {response.status_code}")
            print(f"📝 Response: {response.text}")
            return False, None, None
            
    except requests.exceptions.Timeout:
        print("❌ Таймаут запроса")
        return False, None, None
    except requests.exceptions.ConnectionError:
        print("❌ Ошибка подключения к серверу")
        return False, None, None
    except Exception as e:
        print(f"❌ Неожиданная ошибка: {e}")
        return False, None, None

def main():
    """Основная функция"""
    
    print("=" * 60)
    print("🎯 ГЕНЕРАТОР VPN КОНФИГУРАЦИЙ ДЛЯ НОВЫХ ПОЛЬЗОВАТЕЛЕЙ")
    print("=" * 60)
    
    # Генерируем уникальный email для нового пользователя
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    user_email = f"newuser_{timestamp}@example.com"
    
    success, filename, config = generate_config_for_new_user(user_email)
    
    if success:
        print(f"\n🎉 УСПЕХ! Конфигурация создана для пользователя {user_email}")
        print(f"📁 Файл: {filename}")
        
        # Показываем краткую информацию о конфигурации
        if config and 'outbounds' in config:
            selector = next((ob for ob in config['outbounds'] if ob.get('type') == 'selector'), None)
            if selector and 'outbounds' in selector:
                print(f"🔗 Доступные подключения: {', '.join(selector['outbounds'])}")
        
        print("\n💡 Конфигурация готова к использованию в SingBox клиенте!")
        
    else:
        print(f"\n💥 ОШИБКА! Не удалось создать конфигурацию для {user_email}")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
